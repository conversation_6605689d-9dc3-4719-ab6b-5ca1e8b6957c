---
type: "always_apply"
---

<Basic_Rules>

Environments:
- We are working with Python (miniconda).
- Always check if the Virtual Environment Prompt Indicator in the terminal is “sweep”. If not, run “conda activate sweep” to enable the virtual environment. 
- Python interpreter version: 3.12.11
- Some installed packages: nidaqmx, scipy, matplotlib, numpy, black
- **特别注意**，如需安装新的包，请在确保**终端运行于sweep环境**的前提下（环境标识符为“sweep”），使用conda install进行安装（如果终端询问是否确认安装，请输入并发送"y"）。如果conda安装失败或不提供此包，请停止响应并让我手动处理，严禁自行尝试使用pip。
- The "Type Checking Mode" option of "Pyright" has been set to "strict". Please comply with the relevant type checking specifications as strictly as possible. If you encounter problems that you can't solve, please stop responding and let me handle them manually.

---

Main Task:
- 我们正在开发一个"sweeper400" package，它的主要功能是：协同控制NI数据采集卡（使用现有的"nidaqmx" package）和步进电机（使用"MT_API.dll"文件），自动化分步采集空间中不同位置的信号，并对信号进行处理，获取信号的空间分布等信息。
- "sweeper400" package 包含以下子包："measure"（包含NI数据采集相关module），"move"（包含步进电机控制相关module），"analyze"（包含信号和数据处理相关module），"sweeper"（协同调用其他子包，将功能封装为适用于特定任务的专用对象，供外部以简洁的方式使用）

---

Detailed Rules:
- 本包配置了日志管理框架"sweeper400\sweeper400\logger.py"，请在开发中使用它统一进行日志管理，合理为我们的代码配置日志输出，方便你监测代码的运行情况。你可以查看该系统的总览文档"sweeper400\sweeper400\README_LOGGER.md"，和详细使用指南"sweeper400\sweeper400\logger_usage.md"。在开发中，你可以按需修改和充实"logger.py"，但不要忘了同步更新相关文档。
- 请在开发中遵循以下方式："sweeper400" package的所有文件位于根目录的"sweeper400"目录中，测试代码则位于根目录的"tests"目录中。请在"sweeper400\sweeper400"目录中编写各子包/模块源代码（实现所有的函数/类/方法/属性），在"tests"目录中编写测试代码调用"sweeper400" package（已经以开发模式安装，你可以直接import）。
- 如果涉及添加只读属性，默认使用@property装饰器实现。
- 开发过程中，请不要忘记适时更新各级"__init__.py"和配置文件"sweeper400\pyproject.toml"。更新"sweeper400\pyproject.toml"后，如需在sweep环境中更新配置，请使用"python -m pip install -e"重新安装"sweeper400"。
- 不要修改项目根目录下的"pyproject.toml"文件，其与我们目前的工作无关。
- 除非明确要求，否则不要主动创建“使用示例/演示脚本”、“使用指南/说明文档”，只要将代码注释写得清楚一点即可。

</Basic_Rules>