# sweeper400 日志管理系统

## 系统概述

为 sweeper400 包设计的统一日志管理系统，专门针对中等规模项目的需求，提供清晰、可控、易用的日志功能。

## 核心特性

### ✅ 1. 仅Terminal输出
- 所有日志仅在终端显示，无本地文件保存
- 方便开发时直接查看和调试
- 节省磁盘空间，无需复杂的文件管理

### ✅ 2. 清晰的来源标识
- 明确显示日志来自哪个模块/类/方法
- 支持层级化的命名：`模块.类.方法`
- 自动简化长模块名显示（超过30字符时显示为 `...最后两级`）

### ✅ 3. 可调整的可见性
- **全局级别控制**：一键切换整个系统的日志级别
- **模块特定控制**：为不同模块设置不同的日志级别
- **便捷模式切换**：调试模式、正常模式、安静模式

### ✅ 4. 彩色输出和格式化
- 不同级别使用不同颜色（DEBUG=青色, INFO=绿色, WARNING=黄色, ERROR=红色, CRITICAL=紫色）
- 统一的时间戳格式（精确到毫秒）
- 整齐的列对齐显示

### ✅ 5. 类型安全和现代Python特性
- 完整的类型注解支持
- 枚举类型的日志级别定义
- 符合Python logging最佳实践

## 文件结构

```
sweeper400/sweeper400/
├── logger.py              # 核心日志管理系统
├── logger_usage.md        # 详细使用指南
└── __init__.py            # 包级别导出

```

## 快速开始

### 基本使用
```python
from sweeper400 import get_logger

logger = get_logger(__name__)
logger.info("这是一条信息日志")
```

### 在类中使用
```python
from sweeper400 import get_logger

class MyClass:
    def __init__(self):
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        self.logger.info("类初始化完成")
    
    def my_method(self):
        method_logger = get_logger(f"{__name__}.{self.__class__.__name__}.my_method")
        method_logger.debug("方法开始执行")
        # ... 实际代码 ...
        method_logger.info("方法执行完成")
```

### 级别控制
```python
from sweeper400 import set_debug_mode, set_quiet_mode, set_module_log_level, LogLevel

# 全局控制
set_debug_mode()    # 显示所有日志
set_quiet_mode()    # 只显示警告和错误

# 模块特定控制
set_module_log_level("sweeper400.measure", LogLevel.DEBUG)
set_module_log_level("sweeper400.move", LogLevel.WARNING)
```

## 日志级别说明

| 级别 | 数值 | 颜色 | 用途 |
|------|------|------|------|
| DEBUG | 10 | 青色 | 详细的调试信息、变量值、执行流程 |
| INFO | 20 | 绿色 | 一般信息、状态变化、操作完成 |
| WARNING | 30 | 黄色 | 警告信息、潜在问题、性能提醒 |
| ERROR | 40 | 红色 | 错误信息，程序可继续运行 |
| CRITICAL | 50 | 紫色 | 严重错误，程序可能无法继续 |

## 输出格式示例

```
[14:07:07.881] INFO     sweeper400.measure.DataCollector | 开始数据采集
[14:07:07.885] WARNING  sweeper400.move.StepperMotor     | 检测到轻微阻力
[14:07:07.890] DEBUG    ...MeasureClass.collect_data     | 采集第 1 组数据
```

格式说明：
- `[时间戳]`：精确到毫秒
- `级别`：8字符宽度，颜色编码
- `模块名`：30字符宽度，超长时简化显示
- `消息`：实际日志内容

## API 参考

### 核心函数
- `get_logger(name: str) -> logging.Logger`：获取日志器
- `set_global_log_level(level: LogLevel)`：设置全局级别
- `set_module_log_level(module_name: str, level: LogLevel)`：设置模块级别

### 便捷函数
- `set_debug_mode()`：开启调试模式
- `set_normal_mode()`：正常模式（默认）
- `set_quiet_mode()`：安静模式
- `get_log_levels() -> Dict[str, Any]`：查看当前配置
- `reset_log_levels()`：重置所有配置

### 枚举类型
- `LogLevel.DEBUG`、`LogLevel.INFO`、`LogLevel.WARNING`、`LogLevel.ERROR`、`LogLevel.CRITICAL`

