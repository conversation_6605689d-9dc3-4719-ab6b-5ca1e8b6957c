"""
sweeper400 日志管理系统

提供统一的日志管理功能，支持：
1. 仅Terminal输出，无本地文件保存
2. 清晰的模块/类/方法来源标识
3. 可调整的日志级别和可见性
4. 层级化的日志管理

使用示例:
    from sweeper400.logger import get_logger

    logger = get_logger(__name__)
    logger.info("这是一条信息日志")

    # 在类中使用
    class MyClass:
        def __init__(self):
            self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")

        def my_method(self):
            method_logger = get_logger(f"{__name__}.{self.__class__.__name__}.my_method")
            method_logger.debug("方法执行开始")
"""

import logging
import sys
from typing import Dict, Optional, TextIO, Any
from enum import Enum


class LogLevel(Enum):
    """日志级别枚举"""

    DEBUG = logging.DEBUG  # 10 - 详细的调试信息
    INFO = logging.INFO  # 20 - 一般信息
    WARNING = logging.WARNING  # 30 - 警告信息
    ERROR = logging.ERROR  # 40 - 错误信息
    CRITICAL = logging.CRITICAL  # 50 - 严重错误


class SweeperFormatter(logging.Formatter):
    """自定义日志格式化器"""

    # 不同级别的颜色代码
    COLORS = {
        "DEBUG": "\033[36m",  # 青色
        "INFO": "\033[32m",  # 绿色
        "WARNING": "\033[33m",  # 黄色
        "ERROR": "\033[31m",  # 红色
        "CRITICAL": "\033[35m",  # 紫色
        "RESET": "\033[0m",  # 重置颜色
    }

    def format(self, record):
        # 获取颜色
        color = self.COLORS.get(record.levelname, self.COLORS["RESET"])
        reset = self.COLORS["RESET"]

        # 格式化时间戳（Windows兼容）
        import datetime

        dt = datetime.datetime.fromtimestamp(record.created)
        timestamp = dt.strftime("%H:%M:%S.%f")[:-3]  # 精确到毫秒

        # 简化模块名显示（只显示最后两级）
        name_parts = record.name.split(".")
        if len(name_parts) > 2:
            display_name = "..." + ".".join(name_parts[-2:])
        else:
            display_name = record.name

        # 构建日志消息
        formatted_message = (
            f"{color}[{timestamp}] "
            f"{record.levelname:8s} "
            f"{display_name:30s} | "
            f"{record.getMessage()}{reset}"
        )

        # 如果有异常信息，添加异常堆栈
        if record.exc_info:
            formatted_message += "\n" + self.formatException(record.exc_info)

        return formatted_message


class SweeperLoggerManager:
    """sweeper400 日志管理器"""

    def __init__(self):
        self._loggers: Dict[str, logging.Logger] = {}
        self._global_level = LogLevel.INFO
        self._module_levels: Dict[str, LogLevel] = {}
        self._handler: Optional[logging.StreamHandler[TextIO]] = None
        self._setup_handler()

    def _setup_handler(self):
        """设置日志处理器"""
        if self._handler is None:
            self._handler = logging.StreamHandler(sys.stdout)
            self._handler.setFormatter(SweeperFormatter())

    def get_logger(self, name: str) -> logging.Logger:
        """
        获取指定名称的日志器

        Args:
            name: 日志器名称，通常使用 __name__ 或 模块.类.方法 格式

        Returns:
            配置好的日志器实例
        """
        if name not in self._loggers:
            logger = logging.getLogger(name)
            logger.handlers.clear()  # 清除默认处理器
            if self._handler is not None:
                logger.addHandler(self._handler)
            logger.propagate = False  # 防止重复输出

            # 设置日志级别
            effective_level = self._get_effective_level(name)
            logger.setLevel(effective_level.value)

            self._loggers[name] = logger

        return self._loggers[name]

    def _get_effective_level(self, name: str) -> LogLevel:
        """获取有效的日志级别"""
        # 检查是否有特定模块的级别设置
        name_parts = name.split(".")
        for i in range(len(name_parts), 0, -1):
            partial_name = ".".join(name_parts[:i])
            if partial_name in self._module_levels:
                return self._module_levels[partial_name]

        # 返回全局级别
        return self._global_level

    def set_global_level(self, level: LogLevel):
        """
        设置全局日志级别

        Args:
            level: 日志级别
        """
        self._global_level = level
        # 更新所有已存在的日志器
        for name, logger in self._loggers.items():
            if name not in self._module_levels:
                logger.setLevel(level.value)

    def set_module_level(self, module_name: str, level: LogLevel):
        """
        设置特定模块的日志级别

        Args:
            module_name: 模块名称
            level: 日志级别
        """
        self._module_levels[module_name] = level

        # 更新匹配的日志器
        for name, logger in self._loggers.items():
            if name.startswith(module_name):
                logger.setLevel(level.value)

    def get_current_levels(self) -> Dict[str, Any]:
        """获取当前的日志级别配置"""
        result = {
            "global_level": self._global_level.name,
            "module_levels": {k: v.name for k, v in self._module_levels.items()},
        }
        return result

    def reset_levels(self):
        """重置所有日志级别为默认值"""
        self._global_level = LogLevel.INFO
        self._module_levels.clear()

        # 更新所有日志器
        for logger in self._loggers.values():
            logger.setLevel(LogLevel.INFO.value)


# 全局日志管理器实例
_logger_manager = SweeperLoggerManager()


def get_logger(name: str) -> logging.Logger:
    """
    获取日志器的便捷函数

    Args:
        name: 日志器名称，建议使用 __name__

    Returns:
        配置好的日志器实例

    Example:
        logger = get_logger(__name__)
        logger.info("这是一条信息")
    """
    return _logger_manager.get_logger(name)


def set_global_log_level(level: LogLevel):
    """设置全局日志级别"""
    _logger_manager.set_global_level(level)


def set_module_log_level(module_name: str, level: LogLevel):
    """设置特定模块的日志级别"""
    _logger_manager.set_module_level(module_name, level)


def get_log_levels() -> Dict[str, Any]:
    """获取当前日志级别配置"""
    return _logger_manager.get_current_levels()


def reset_log_levels():
    """重置所有日志级别"""
    _logger_manager.reset_levels()


# 便捷的日志级别调整函数
def set_debug_mode():
    """开启调试模式（显示所有日志）"""
    set_global_log_level(LogLevel.DEBUG)


def set_quiet_mode():
    """安静模式（只显示警告和错误）"""
    set_global_log_level(LogLevel.WARNING)


def set_normal_mode():
    """正常模式（显示信息、警告和错误）"""
    set_global_log_level(LogLevel.INFO)
